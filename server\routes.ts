import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { googleAuthService } from "./services/google-auth";
import { reviewFetcherService } from "./services/review-fetcher";
import { updateReviewResponseSchema } from "@shared/schema";
import cron from 'node-cron';

// Background business data fetching
async function fetchBusinessDataInBackground(refreshToken: string): Promise<void> {
  let attempts = 0;
  const maxAttempts = 3;
  
  while (attempts < maxAttempts) {
    try {
      console.log(`Attempting to fetch business data (attempt ${attempts + 1}/${maxAttempts})`);
      
      // Set up authentication
      googleAuthService.setCredentials({ refresh_token: refreshToken });
      await googleAuthService.refreshAccessToken();
      
      // Get business accounts
      const accounts = await googleAuthService.getBusinessAccounts();
      
      if (accounts.length > 0) {
        const account = accounts[0];
        console.log('Successfully fetched business account:', account.accountName);
        
        // Get business locations
        const locations = await googleAuthService.getBusinessLocations(account.name);
        
        if (locations.length > 0) {
          const location = locations[0];
          console.log('Successfully fetched business location:', location.title);
          
          // Update business info with real data
          await storage.createOrUpdateBusinessInfo({
            name: location.title || account.accountName || 'Your Business',
            address: location.storefrontAddress?.addressLines?.join(', ') || 'Address not available',
            accountId: account.name || '',
            locationId: location.name || '',
            refreshToken: refreshToken,
            lastSync: null
          });
          
          console.log('Business data updated successfully - triggering review sync');
          
          // Trigger review sync
          setTimeout(async () => {
            const syncResult = await reviewFetcherService.syncReviews();
            console.log('Review sync result:', syncResult);
          }, 1000);
          
          return;
        }
      }
      
      attempts++;
      if (attempts < maxAttempts) {
        const delay = 5000 * attempts;
        console.log(`Waiting ${delay/1000}s before retry...`);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
    } catch (error: any) {
      attempts++;
      console.log(`Business data fetch attempt ${attempts} failed:`, error.message);
      
      if (attempts < maxAttempts) {
        const delay = 5000 * attempts;
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }
  
  console.log('Business data fetch failed after all attempts');
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Setup automated review fetching every 30 minutes
  cron.schedule('*/30 * * * *', () => {
    console.log('🔄 Running scheduled review sync...');
    reviewFetcherService.syncReviews().then(result => {
      if (result.success) {
        console.log(`✅ Sync completed: ${result.newReviews} new reviews`);
      } else {
        console.log(`❌ Sync failed: ${result.error}`);
      }
    });
  });

  // Get business info and setup status
  app.get("/api/business", async (req, res) => {
    try {
      const businessInfo = await storage.getBusinessInfo();
      const isSetup = businessInfo && businessInfo.refreshToken;

      res.json({
        businessInfo,
        isSetup,
        setupUrl: isSetup ? null : googleAuthService.generateAuthUrl()
      });
    } catch (error) {
      console.log('No business info found (fresh start):', error);
      // Return default response for fresh setup
      res.json({
        businessInfo: null,
        isSetup: false,
        setupUrl: googleAuthService.generateAuthUrl()
      });
    }
  });

  // OAuth callback (both routes for compatibility)
  const handleOAuthCallback = async (req: any, res: any) => {
    try {
      const { code, error } = req.query;
      
      if (error) {
        console.error('OAuth error:', error);
        return res.redirect('/?setup=error&message=' + encodeURIComponent(error as string));
      }
      
      if (!code || typeof code !== 'string') {
        return res.redirect('/?setup=error&message=' + encodeURIComponent('Missing authorization code'));
      }

      console.log('Processing OAuth callback with code:', code.substring(0, 20) + '...');

      const tokens = await googleAuthService.getTokens(code);
      console.log('Successfully obtained tokens');
      
      try {
        // Get business accounts and locations
        const accounts = await googleAuthService.getBusinessAccounts();
        console.log('Found business accounts:', accounts.length);
        
        if (accounts.length === 0) {
          // Save tokens but mark as pending business data fetch
          await storage.createOrUpdateBusinessInfo({
            name: 'Loading Business Information...',
            address: 'Fetching business details from Google...',
            accountId: 'pending-fetch',
            locationId: 'pending-fetch',
            refreshToken: tokens.refresh_token || '',
            lastSync: null
          });
          
          console.log('✅ Authentication successful - will fetch business data in background');
          
          // Schedule immediate background fetch
          setTimeout(async () => {
            await fetchBusinessDataInBackground(tokens.refresh_token || '');
          }, 2000);
          
          return res.redirect('/dashboard');
        }

        const account = accounts[0];
        const locations = await googleAuthService.getBusinessLocations(account.name);
        console.log('Found business locations:', locations.length);
        
        if (locations.length === 0) {
          // Save account info even without locations
          await storage.createOrUpdateBusinessInfo({
            name: account.accountName || 'Your Business',
            address: 'Location details pending',
            accountId: account.name || '',
            locationId: 'no-location-found',
            refreshToken: tokens.refresh_token || '',
            lastSync: null
          });
          
          console.log('✅ Account connected - dashboard enabled');
          return res.redirect('/dashboard');
        }

        const location = locations[0];
        console.log('Using business location:', location.title);
        
        // Save complete business configuration
        await storage.createOrUpdateBusinessInfo({
          name: location.title || 'Unknown Business',
          address: location.storefrontAddress?.addressLines?.join(', ') || '',
          accountId: account.name || '',
          locationId: location.name || '',
          refreshToken: tokens.refresh_token || '',
          lastSync: null
        });

        console.log('✅ Complete business setup successful');

        // Attempt initial sync
        try {
          const syncResult = await reviewFetcherService.syncReviews();
          console.log('Initial sync result:', syncResult);
        } catch (syncError) {
          console.log('Initial sync skipped due to quota limits - will retry automatically');
        }

        res.redirect('/dashboard');
        
      } catch (businessError) {
        console.log('⚠️ Business API quota exceeded - proceeding with basic authentication');
        
        // Save minimal business info to enable dashboard access with valid tokens
        await storage.createOrUpdateBusinessInfo({
          name: 'Your Business Profile',
          address: 'Details will load when quota resets',
          accountId: 'quota-limited',
          locationId: 'quota-limited',
          refreshToken: tokens.refresh_token || '',
          lastSync: null
        });
        
        console.log('✅ Google authentication successful - dashboard ready');
        res.redirect('/dashboard');
      }
    } catch (error) {
      console.error('OAuth callback error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      res.redirect('/?setup=error&message=' + encodeURIComponent(errorMessage));
    }
  };

  // Register both callback routes for compatibility
  app.get("/api/auth/callback", handleOAuthCallback);
  app.get("/api/auth/google/callback", handleOAuthCallback);

  // Get reviews with filtering
  app.get("/api/reviews", async (req, res) => {
    try {
      const { rating, sentiment, limit = '20', offset = '0' } = req.query;
      
      const filters = {
        rating: rating ? parseInt(rating as string) : undefined,
        sentiment: sentiment as string,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      };

      const reviews = await storage.getReviews(filters);
      res.json(reviews);
    } catch (error) {
      res.status(500).json({ message: "Failed to get reviews" });
    }
  });

  // Get review statistics
  app.get("/api/reviews/stats", async (req, res) => {
    try {
      const stats = await storage.getReviewStats();
      res.json(stats);
    } catch (error) {
      res.status(500).json({ message: "Failed to get review stats" });
    }
  });

  // Add response to review
  app.post("/api/reviews/:id/response", async (req, res) => {
    try {
      const reviewId = parseInt(req.params.id);
      const body = updateReviewResponseSchema.parse(req.body);
      
      // Get the review to find its Google ID
      const review = await storage.getReviews();
      const targetReview = review.find(r => r.id === reviewId);
      
      if (!targetReview) {
        return res.status(404).json({ message: "Review not found" });
      }

      // Get business info for authentication
      const businessInfo = await storage.getBusinessInfo();
      
      if (!businessInfo || !businessInfo.refreshToken) {
        return res.status(400).json({ message: "Google Business account not configured" });
      }

      // Set up authentication for Google API
      googleAuthService.setCredentials({ refresh_token: businessInfo.refreshToken });
      await googleAuthService.refreshAccessToken();

      // Post response to Google My Business
      try {
        await googleAuthService.postReviewResponse(targetReview.googleId, body.responseComment);
        console.log(`Posted response to Google for review ${targetReview.googleId}`);
      } catch (googleError) {
        console.error('Failed to post to Google:', googleError);
        // Continue to update local storage even if Google API fails
      }
      
      // Update local storage
      const updatedReview = await storage.updateReview(reviewId, {
        responseComment: body.responseComment,
        responseDate: new Date()
      });
      
      res.json(updatedReview);
    } catch (error) {
      console.error('Response error:', error);
      res.status(400).json({ message: "Failed to add response" });
    }
  });

  // Manual sync trigger
  app.post("/api/sync", async (req, res) => {
    try {
      const result = await reviewFetcherService.syncReviews();
      res.json(result);
    } catch (error) {
      res.status(500).json({ message: "Sync failed" });
    }
  });

  // Force business data refresh
  app.post("/api/refresh-business", async (req, res) => {
    try {
      const businessInfo = await storage.getBusinessInfo();
      
      if (!businessInfo || !businessInfo.refreshToken) {
        return res.json({ success: false, error: 'No authentication found. Please reconnect your Google account.' });
      }

      console.log('🔄 Manual business data refresh requested...');
      await fetchBusinessDataInBackground(businessInfo.refreshToken);
      
      const updatedInfo = await storage.getBusinessInfo();
      res.json({ success: true, businessInfo: updatedInfo });
    } catch (error) {
      console.error('Business refresh error:', error);
      res.json({ success: false, error: (error as Error).message });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
