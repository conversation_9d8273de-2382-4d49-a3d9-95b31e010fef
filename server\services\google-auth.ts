import { google } from 'googleapis';

export class GoogleAuthService {
  private oauth2Client: any;

  constructor() {
    // Debug environment variables
    console.log('Environment variables check:');
    console.log('GOOGLE_REDIRECT_URI:', process.env.GOOGLE_REDIRECT_URI);
    console.log('REPLIT_DOMAINS:', process.env.REPLIT_DOMAINS);

    // Use environment variable for redirect URI, with fallbacks
    const redirectUri = process.env.GOOGLE_REDIRECT_URI
      || (process.env.REPLIT_DOMAINS
          ? `https://${process.env.REPLIT_DOMAINS.split(',')[0]}/api/auth/callback`
          : 'http://localhost:3000/api/auth/callback');

    // Load Google OAuth credentials - fallback to direct values if env vars not available
    const clientId = process.env.GOOGLE_CLIENT_ID || '471900131350-7c4dg56v2oa9qm36ikndd358h26qv5di.apps.googleusercontent.com';
    const clientSecret = process.env.GOOGLE_CLIENT_SECRET || '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';

    console.log('OAuth Redirect URI:', redirectUri);
    console.log('Google Client ID loaded:', clientId.substring(0, 10) + '...');
    
    this.oauth2Client = new google.auth.OAuth2(
      clientId,
      clientSecret,
      redirectUri
    );
  }

  generateAuthUrl(): string {
    return this.oauth2Client.generateAuthUrl({
      access_type: 'offline',
      scope: [
        'https://www.googleapis.com/auth/business.manage',
        'https://www.googleapis.com/auth/plus.business.manage'
      ],
      prompt: 'consent'
    });
  }

  async getReviews(locationName: string) {
    try {
      google.options({ auth: this.oauth2Client });

      // Use the Google My Business API v4 for reviews
      const response = await this.oauth2Client.request({
        url: `https://mybusiness.googleapis.com/v4/${locationName}/reviews`,
        method: 'GET',
        params: {
          pageSize: 50
        }
      });

      return response.data.reviews || [];
    } catch (error: any) {
      console.error('Error getting reviews:', error.message);

      // Handle quota exceeded specifically
      if (error.message && error.message.includes('Quota exceeded')) {
        console.log('⚠️ Google API quota exceeded for reviews. Will retry later...');
        return [];
      }

      throw new Error('Unable to fetch reviews. Please ensure you have the necessary permissions.');
    }
  }

  async getTokens(code: string) {
    try {
      const { tokens } = await this.oauth2Client.getToken(code);
      this.oauth2Client.setCredentials(tokens);
      return tokens;
    } catch (error: any) {
      console.error('Error getting tokens:', error.message);
      if (error.message.includes('invalid_grant')) {
        throw new Error('Authorization code expired or already used. Please try authorizing again.');
      }
      throw error;
    }
  }

  setCredentials(tokens: any) {
    this.oauth2Client.setCredentials(tokens);
  }

  async refreshAccessToken() {
    await this.oauth2Client.getAccessToken();
  }

  getAuthClient() {
    return this.oauth2Client;
  }

  async getBusinessAccounts() {
    try {
      // Add delay to prevent quota issues
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      google.options({ auth: this.oauth2Client });
      
      // Use the correct Google My Business Account Management API
      const response = await this.oauth2Client.request({
        url: 'https://mybusinessaccountmanagement.googleapis.com/v1/accounts',
        method: 'GET'
      });
      
      return response.data.accounts || [];
    } catch (error: any) {
      console.error('Error getting business accounts:', error.message);
      
      // Handle quota exceeded specifically
      if (error.message && error.message.includes('Quota exceeded')) {
        console.log('⚠️ Google API quota exceeded. Will retry in background...');
        // Return empty array to trigger retry logic in callback
        return [];
      }
      
      throw new Error('Unable to access business accounts. Make sure you have a Google Business Profile set up and the necessary APIs are enabled.');
    }
  }

  async getBusinessLocations(accountName: string) {
    try {
      google.options({ auth: this.oauth2Client });
      
      // Use the correct Google My Business Business Information API
      const response = await this.oauth2Client.request({
        url: `https://mybusinessbusinessinformation.googleapis.com/v1/${accountName}/locations`,
        method: 'GET'
      });
      
      return response.data.locations || [];
    } catch (error: any) {
      console.error('Error getting business locations:', error.message);
      throw new Error('Unable to find business locations. Please ensure your Google Business Profile is properly set up and verified.');
    }
  }

  async postReviewResponse(reviewName: string, responseComment: string) {
    try {
      google.options({ auth: this.oauth2Client });
      
      const response = await this.oauth2Client.request({
        url: `https://mybusinessreviews.googleapis.com/v1/${reviewName}/reply`,
        method: 'PUT',
        data: {
          comment: responseComment
        }
      });
      
      return response.data;
    } catch (error: any) {
      console.error('Error posting review response:', error.message);
      throw new Error('Unable to post response to Google. Please ensure you have the necessary permissions.');
    }
  }
}

let _googleAuthService: GoogleAuthService | null = null;

export const googleAuthService = {
  get instance(): GoogleAuthService {
    if (!_googleAuthService) {
      _googleAuthService = new GoogleAuthService();
    }
    return _googleAuthService;
  },

  // Proxy methods to the instance
  generateAuthUrl(): string {
    return this.instance.generateAuthUrl();
  },

  async getTokens(code: string) {
    return this.instance.getTokens(code);
  },

  setCredentials(tokens: any) {
    return this.instance.setCredentials(tokens);
  },

  async refreshAccessToken() {
    return this.instance.refreshAccessToken();
  },

  getAuthClient() {
    return this.instance.getAuthClient();
  },

  async getBusinessAccounts() {
    return this.instance.getBusinessAccounts();
  },

  async getBusinessLocations(accountName: string) {
    return this.instance.getBusinessLocations(accountName);
  },

  async getReviews(locationName: string) {
    return this.instance.getReviews(locationName);
  },

  async postReviewResponse(reviewName: string, responseComment: string) {
    return this.instance.postReviewResponse(reviewName, responseComment);
  }
};
