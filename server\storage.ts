import type {
  User,
  InsertUser,
  BusinessInfo,
  InsertBusinessInfo,
  Review,
  InsertReview
} from "@shared/schema";
import { drizzle } from "drizzle-orm/better-sqlite3";
import { users, businessInfo, reviews } from "@shared/schema";
import { eq, desc, and, count, avg, sql } from "drizzle-orm";
import Database from "better-sqlite3";
import { drizzle } from "drizzle-orm/better-sqlite3";
import { users, businessInfo, reviews } from "@shared/schema";
import { eq, desc, and, count, avg, sql } from "drizzle-orm";
import Database from "better-sqlite3";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getBusinessInfo(): Promise<BusinessInfo | undefined>;
  createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo>;
  
  getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]>;
  getReviewByGoogleId(googleId: string): Promise<Review | undefined>;
  createReview(review: InsertReview): Promise<Review>;
  updateReview(id: number, updates: Partial<Review>): Promise<Review>;
  getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }>;
}

export class SQLiteStorage implements IStorage {
  private db: ReturnType<typeof drizzle>;

  constructor(databasePath: string = "./database.sqlite") {
    const sqlite = new Database(databasePath);
    this.db = drizzle(sqlite);
    console.log(`SQLite database initialized at ${databasePath}`);
  }

  async getUser(id: number): Promise<User | undefined> {
    const result = await this.db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await this.db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const result = await this.db.insert(users).values(insertUser).returning();
    return result[0];
  }

  async getBusinessInfo(): Promise<BusinessInfo | undefined> {
    const result = await this.db.select().from(businessInfo).limit(1);
    return result[0];
  }

  async createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo> {
    const existing = await this.getBusinessInfo();

    if (existing) {
      const result = await this.db
        .update(businessInfo)
        .set({
          name: info.name,
          address: info.address,
          accountId: info.accountId,
          locationId: info.locationId,
          refreshToken: info.refreshToken,
          lastSync: info.lastSync ? info.lastSync.toISOString() : null,
        })
        .where(eq(businessInfo.id, existing.id))
        .returning();
      return result[0];
    } else {
      const result = await this.db
        .insert(businessInfo)
        .values({
          ...info,
          lastSync: info.lastSync ? info.lastSync.toISOString() : null,
        })
        .returning();
      return result[0];
    }
  }

  async getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]> {
    let query = this.db.select().from(reviews);

    const conditions = [];
    if (filters?.rating) {
      conditions.push(eq(reviews.rating, filters.rating));
    }
    if (filters?.sentiment) {
      conditions.push(eq(reviews.sentiment, filters.sentiment));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    query = query.orderBy(desc(reviews.reviewDate));

    if (filters?.limit) {
      query = query.limit(filters.limit);
    }
    if (filters?.offset) {
      query = query.offset(filters.offset);
    }

    const result = await query;

    // Convert date strings back to Date objects
    return result.map(review => ({
      ...review,
      reviewDate: new Date(review.reviewDate),
      responseDate: review.responseDate ? new Date(review.responseDate) : null,
      fetchedAt: new Date(review.fetchedAt),
    }));
  }

  async getReviewByGoogleId(googleId: string): Promise<Review | undefined> {
    const result = await this.db.select().from(reviews).where(eq(reviews.googleId, googleId)).limit(1);
    if (!result[0]) return undefined;

    const review = result[0];
    return {
      ...review,
      reviewDate: new Date(review.reviewDate),
      responseDate: review.responseDate ? new Date(review.responseDate) : null,
      fetchedAt: new Date(review.fetchedAt),
    };
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    const result = await this.db
      .insert(reviews)
      .values({
        ...insertReview,
        reviewDate: insertReview.reviewDate.toISOString(),
        responseDate: insertReview.responseDate ? insertReview.responseDate.toISOString() : null,
        fetchedAt: insertReview.fetchedAt.toISOString(),
      })
      .returning();

    const review = result[0];
    return {
      ...review,
      reviewDate: new Date(review.reviewDate),
      responseDate: review.responseDate ? new Date(review.responseDate) : null,
      fetchedAt: new Date(review.fetchedAt),
    };
  }

  async updateReview(id: number, updates: Partial<Review>): Promise<Review> {
    const updateData: any = { ...updates };

    // Convert Date objects to ISO strings for SQLite
    if (updateData.reviewDate) {
      updateData.reviewDate = updateData.reviewDate.toISOString();
    }
    if (updateData.responseDate) {
      updateData.responseDate = updateData.responseDate.toISOString();
    }
    if (updateData.fetchedAt) {
      updateData.fetchedAt = updateData.fetchedAt.toISOString();
    }

    const result = await this.db
      .update(reviews)
      .set(updateData)
      .where(eq(reviews.id, id))
      .returning();

    const review = result[0];
    return {
      ...review,
      reviewDate: new Date(review.reviewDate),
      responseDate: review.responseDate ? new Date(review.responseDate) : null,
      fetchedAt: new Date(review.fetchedAt),
    };
  }

  async getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }> {
    const allReviews = await this.db.select().from(reviews);

    if (allReviews.length === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingCounts: [0, 0, 0, 0, 0],
        sentimentCounts: { positive: 0, neutral: 0, negative: 0 },
        responseRate: 0,
        thisMonth: 0
      };
    }

    const totalReviews = allReviews.length;
    const averageRating = allReviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews;

    const ratingCounts = [0, 0, 0, 0, 0];
    allReviews.forEach(r => {
      if (r.rating >= 1 && r.rating <= 5) {
        ratingCounts[r.rating - 1]++;
      }
    });

    const sentimentCounts = allReviews.reduce((acc, r) => {
      acc[r.sentiment as keyof typeof acc]++;
      return acc;
    }, { positive: 0, neutral: 0, negative: 0 });

    const reviewsWithResponses = allReviews.filter(r => r.responseComment).length;
    const responseRate = totalReviews > 0 ? (reviewsWithResponses / totalReviews) * 100 : 0;

    const thisMonth = allReviews.filter(r => {
      const reviewDate = new Date(r.reviewDate);
      const now = new Date();
      return reviewDate.getMonth() === now.getMonth() &&
             reviewDate.getFullYear() === now.getFullYear();
    }).length;

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingCounts,
      sentimentCounts,
      responseRate: Math.round(responseRate),
      thisMonth
    };
  }
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private business: BusinessInfo | undefined;
  private reviewsList: Review[];
  private currentId: number;
  private currentReviewId: number;

  constructor() {
    this.users = new Map();
    this.business = undefined;
    this.reviewsList = [];
    this.currentId = 1;
    this.currentReviewId = 1;
    
    console.log('Storage initialized - ready for live Google Business data');
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getBusinessInfo(): Promise<BusinessInfo | undefined> {
    return this.business;
  }

  async createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo> {
    const businessInfo: BusinessInfo = {
      id: this.business?.id || 1,
      name: info.name,
      address: info.address ?? null,
      accountId: info.accountId,
      locationId: info.locationId,
      refreshToken: info.refreshToken ?? null,
      lastSync: info.lastSync ?? null,
    };
    this.business = businessInfo;
    return businessInfo;
  }

  async getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]> {
    let filtered = [...this.reviewsList];
    
    if (filters?.rating) {
      filtered = filtered.filter(r => r.rating === filters.rating);
    }
    
    if (filters?.sentiment) {
      filtered = filtered.filter(r => r.sentiment === filters.sentiment);
    }
    
    // Sort by review date descending
    filtered.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime());
    
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 20;
    
    return filtered.slice(offset, offset + limit);
  }

  async getReviewByGoogleId(googleId: string): Promise<Review | undefined> {
    return this.reviewsList.find(r => r.googleId === googleId);
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    const id = this.currentReviewId++;
    const review: Review = { 
      id,
      googleId: insertReview.googleId,
      rating: insertReview.rating,
      comment: insertReview.comment ?? null,
      reviewerName: insertReview.reviewerName,
      reviewDate: insertReview.reviewDate,
      responseComment: insertReview.responseComment ?? null,
      responseDate: insertReview.responseDate ?? null,
      sentiment: insertReview.sentiment,
      fetchedAt: insertReview.fetchedAt
    };
    this.reviewsList.push(review);
    return review;
  }

  async updateReview(id: number, updates: Partial<Review>): Promise<Review> {
    const index = this.reviewsList.findIndex(r => r.id === id);
    if (index === -1) {
      throw new Error('Review not found');
    }
    
    this.reviewsList[index] = { ...this.reviewsList[index], ...updates };
    return this.reviewsList[index];
  }

  async getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }> {
    const reviews = this.reviewsList;
    const totalReviews = reviews.length;
    
    if (totalReviews === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingCounts: [0, 0, 0, 0, 0],
        sentimentCounts: { positive: 0, neutral: 0, negative: 0 },
        responseRate: 0,
        thisMonth: 0
      };
    }

    const averageRating = reviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
    
    const ratingCounts = [0, 0, 0, 0, 0];
    reviews.forEach(r => {
      if (r.rating >= 1 && r.rating <= 5) {
        ratingCounts[r.rating - 1]++;
      }
    });

    const sentimentCounts = reviews.reduce((acc, r) => {
      acc[r.sentiment as keyof typeof acc]++;
      return acc;
    }, { positive: 0, neutral: 0, negative: 0 });

    const reviewsWithResponses = reviews.filter(r => r.responseComment).length;
    const responseRate = totalReviews > 0 ? (reviewsWithResponses / totalReviews) * 100 : 0;

    const thisMonth = reviews.filter(r => {
      const reviewDate = new Date(r.reviewDate);
      const now = new Date();
      return reviewDate.getMonth() === now.getMonth() && 
             reviewDate.getFullYear() === now.getFullYear();
    }).length;

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingCounts,
      sentimentCounts,
      responseRate: Math.round(responseRate),
      thisMonth
    };
  }
}

// Use SQLite storage by default, fallback to MemStorage if SQLite fails
let storage: IStorage;
try {
  storage = new SQLiteStorage();
  console.log('Using SQLite storage');
} catch (error) {
  console.warn('Failed to initialize SQLite storage, falling back to memory storage:', error);
  storage = new MemStorage();
}

export { storage };