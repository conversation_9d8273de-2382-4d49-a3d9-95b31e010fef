import type {
  User,
  InsertUser,
  BusinessInfo,
  InsertBusinessInfo,
  Review,
  InsertReview
} from "@shared/schema";
import { drizzle } from "drizzle-orm/better-sqlite3";
import { users, businessInfo, reviews } from "@shared/schema";
import { eq, desc, and, count, avg, sql } from "drizzle-orm";
import Database from "better-sqlite3";

export interface IStorage {
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  getBusinessInfo(): Promise<BusinessInfo | undefined>;
  createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo>;
  
  getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]>;
  getReviewByGoogleId(googleId: string): Promise<Review | undefined>;
  createReview(review: InsertReview): Promise<Review>;
  updateReview(id: number, updates: Partial<Review>): Promise<Review>;
  getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }>;
}

export class MemStorage implements IStorage {
  private users: Map<number, User>;
  private business: BusinessInfo | undefined;
  private reviewsList: Review[];
  private currentId: number;
  private currentReviewId: number;

  constructor() {
    this.users = new Map();
    this.business = undefined;
    this.reviewsList = [];
    this.currentId = 1;
    this.currentReviewId = 1;
    
    console.log('Storage initialized - ready for live Google Business data');
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(
      (user) => user.username === username,
    );
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const id = this.currentId++;
    const user: User = { ...insertUser, id };
    this.users.set(id, user);
    return user;
  }

  async getBusinessInfo(): Promise<BusinessInfo | undefined> {
    return this.business;
  }

  async createOrUpdateBusinessInfo(info: InsertBusinessInfo): Promise<BusinessInfo> {
    const businessInfo: BusinessInfo = {
      id: this.business?.id || 1,
      name: info.name,
      address: info.address ?? null,
      accountId: info.accountId,
      locationId: info.locationId,
      refreshToken: info.refreshToken ?? null,
      lastSync: info.lastSync ?? null,
    };
    this.business = businessInfo;
    return businessInfo;
  }

  async getReviews(filters?: { rating?: number; sentiment?: string; limit?: number; offset?: number }): Promise<Review[]> {
    let filtered = [...this.reviewsList];
    
    if (filters?.rating) {
      filtered = filtered.filter(r => r.rating === filters.rating);
    }
    
    if (filters?.sentiment) {
      filtered = filtered.filter(r => r.sentiment === filters.sentiment);
    }
    
    // Sort by review date descending
    filtered.sort((a, b) => new Date(b.reviewDate).getTime() - new Date(a.reviewDate).getTime());
    
    const offset = filters?.offset || 0;
    const limit = filters?.limit || 20;
    
    return filtered.slice(offset, offset + limit);
  }

  async getReviewByGoogleId(googleId: string): Promise<Review | undefined> {
    return this.reviewsList.find(r => r.googleId === googleId);
  }

  async createReview(insertReview: InsertReview): Promise<Review> {
    const id = this.currentReviewId++;
    const review: Review = { 
      id,
      googleId: insertReview.googleId,
      rating: insertReview.rating,
      comment: insertReview.comment ?? null,
      reviewerName: insertReview.reviewerName,
      reviewDate: insertReview.reviewDate,
      responseComment: insertReview.responseComment ?? null,
      responseDate: insertReview.responseDate ?? null,
      sentiment: insertReview.sentiment,
      fetchedAt: insertReview.fetchedAt
    };
    this.reviewsList.push(review);
    return review;
  }

  async updateReview(id: number, updates: Partial<Review>): Promise<Review> {
    const index = this.reviewsList.findIndex(r => r.id === id);
    if (index === -1) {
      throw new Error('Review not found');
    }
    
    this.reviewsList[index] = { ...this.reviewsList[index], ...updates };
    return this.reviewsList[index];
  }

  async getReviewStats(): Promise<{
    totalReviews: number;
    averageRating: number;
    ratingCounts: number[];
    sentimentCounts: { positive: number; neutral: number; negative: number };
    responseRate: number;
    thisMonth: number;
  }> {
    const reviews = this.reviewsList;
    const totalReviews = reviews.length;
    
    if (totalReviews === 0) {
      return {
        totalReviews: 0,
        averageRating: 0,
        ratingCounts: [0, 0, 0, 0, 0],
        sentimentCounts: { positive: 0, neutral: 0, negative: 0 },
        responseRate: 0,
        thisMonth: 0
      };
    }

    const averageRating = reviews.reduce((sum, r) => sum + r.rating, 0) / totalReviews;
    
    const ratingCounts = [0, 0, 0, 0, 0];
    reviews.forEach(r => {
      if (r.rating >= 1 && r.rating <= 5) {
        ratingCounts[r.rating - 1]++;
      }
    });

    const sentimentCounts = reviews.reduce((acc, r) => {
      acc[r.sentiment as keyof typeof acc]++;
      return acc;
    }, { positive: 0, neutral: 0, negative: 0 });

    const reviewsWithResponses = reviews.filter(r => r.responseComment).length;
    const responseRate = totalReviews > 0 ? (reviewsWithResponses / totalReviews) * 100 : 0;

    const thisMonth = reviews.filter(r => {
      const reviewDate = new Date(r.reviewDate);
      const now = new Date();
      return reviewDate.getMonth() === now.getMonth() && 
             reviewDate.getFullYear() === now.getFullYear();
    }).length;

    return {
      totalReviews,
      averageRating: Math.round(averageRating * 10) / 10,
      ratingCounts,
      sentimentCounts,
      responseRate: Math.round(responseRate),
      thisMonth
    };
  }
}

export const storage = new MemStorage();